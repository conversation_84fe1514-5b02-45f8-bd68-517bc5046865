# COLLECT
from datetime import datetime, timedelta
from typing import Sequence
from uuid import UUID

import pandas as pd

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts, SortOrder
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.event_type import EventType
from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.feeling.emotion import Emotion
from services.base.domain.schemas.events.feeling.stress import Stress, StressFields
from services.base.domain.schemas.events.note import Note, NoteFields
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.query.boolean_query import AndQuery, NotQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import PatternQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.usage_statistics_output import (
    UsageStatisticsResultEntity,
)
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector import (
    StatsCollector,
)
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector_contants import (
    DEFAULT_MOOD_EXPLANATION,
)


class StatsCollectorRunner:
    def __init__(
        self,
        collector: StatsCollector,
        search_service: DocumentSearchService,
    ):
        self._collector = collector
        self._search_service = search_service

    async def execute_async(
        self,
        time_gte: datetime,
        time_lte: datetime,
        user_ids: Sequence[UUID] = None,
    ) -> UsageStatisticsResultEntity:
        document_count_df = await self._create_document_count_df(
            datetime_range_gte=time_gte, datetime_range_lte=time_lte
        )

        # Gets information on users, active users over timeframe, new users registered over timeframe and total users at
        # the end of timeframe
        active_users = await self._collector.get_users(
            time_gte=time_gte,
            time_lte=time_lte,
            field="last_logged_at",
            user_ids=user_ids,
        )
        active_anonymous_user = await self._collector.get_users(
            time_gte=time_gte,
            time_lte=time_lte,
            field="last_logged_at",
            user_type=MemberUserType.ANONYMOUS,
            user_ids=user_ids,
        )
        new_users = await self._collector.get_users(
            time_gte=time_gte,
            time_lte=time_lte,
            user_ids=user_ids,
        )
        new_anonymous_users = await self._collector.get_users(
            time_gte=time_gte,
            time_lte=time_lte,
            user_type=MemberUserType.ANONYMOUS,
            user_ids=user_ids,
        )
        total_users = await self._collector.get_users(user_ids=user_ids)
        total_anonymous_users = await self._collector.get_users(
            user_type=MemberUserType.ANONYMOUS,
            user_ids=user_ids,
        )
        stale_users = await self._collector.get_users(
            time_lte=time_gte - timedelta(days=180),
            field="last_logged_at",
            user_ids=user_ids,
        )
        multiple_device_users = await self._collector.get_multiple_device_users()

        users_with_event = await self._get_users_with_events(
            time_gte=time_gte,
            time_lte=time_lte,
            user_ids=user_ids,
        )

        users_with_location = await self._collector.count_users_with_documents(
            data_schema=Location,
            time_gte=time_gte,
            time_lte=time_lte,
            uuid_filter=user_ids,
        )
        users_with_heartrate = await self._collector.count_users_with_documents(
            data_schema=HeartRate,
            time_gte=time_gte,
            time_lte=time_lte,
            uuid_filter=user_ids,
        )

        users_with_notes = await self._get_users_with_notes(
            time_gte=time_gte,
            time_lte=time_lte,
            uuid_filter=user_ids,
        )
        users_with_rating = await self._get_users_with_rating(
            time_gte=time_gte,
            time_lte=time_lte,
            uuid_filter=user_ids,
        )

        return UsageStatisticsResultEntity(
            new_users=new_users,
            new_anonymous_users=new_anonymous_users,
            total_users=total_users,
            total_anonymous_users=total_anonymous_users,
            active_users=active_users,
            active_anonymous_users=active_anonymous_user,
            stale_users=stale_users,
            multiple_device_users=multiple_device_users,
            users_with_notes=users_with_notes,
            users_with_events=users_with_event,
            users_with_rating=users_with_rating,
            users_with_location=users_with_location,
            users_with_heartrate=users_with_heartrate,
            document_count_csv_string=document_count_df.to_csv(index=False),
        )

    async def _create_document_count_df(
        self, datetime_range_gte: datetime, datetime_range_lte: datetime
    ) -> pd.DataFrame:
        organizations = [organization for organization in Organization]
        timestamp_range_query = CommonLeafQueries.timestamp_range_query(gte=datetime_range_gte, lte=datetime_range_lte)
        # TODO missing uuid_filter implementation for testing purposes, mix of V2 and V3 documents, does not get asserted

        data = []
        for event_type in EventType:
            count_dict = {"DataType": event_type.name}
            for organization in organizations:
                organization_query = CommonLeafQueries.organization_values_query(organizations=[organization])
                query = Query(
                    type_queries=[
                        TypeQuery(
                            query=AndQuery(queries=[timestamp_range_query, organization_query]),
                            domain_types=[event_type.to_domain_model()],
                        )
                    ]
                )
                count = await self._search_service.count_by_query(query=query)
                count_dict[organization.name] = count

            count_dict["Total Documents"] = sum(count_dict[organization.name] for organization in organizations)
            data.append(count_dict)

        df = pd.DataFrame(data)

        total_row = {"DataType": "TotalDocuments"}
        for organization in organizations:
            total_row[organization.name] = df[organization.name].sum()

        total_row["Total Documents"] = df["Total Documents"].sum()
        total_df = pd.DataFrame([total_row])

        df = pd.concat([df, total_df], ignore_index=True)
        return df

    async def _get_users_with_events(
        self, time_gte: datetime, time_lte: datetime, user_ids: Sequence[UUID] = None
    ) -> int:
        query = CommonLeafQueries.timestamp_range_query(gte=time_gte, lte=time_lte)
        if user_ids:
            values_query = ValuesQuery(
                field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}",
                values=[str(user_id) for user_id in user_ids],
            )
            query = AndQuery(queries=[query, values_query])

        single_query = SingleDocumentTypeQuery[Event](query=query, domain_type=Event)
        search_results = await self._search_service.search_documents_by_single_query(
            size=10000,
            query=single_query,
            sorts=[CommonSorts.timestamp(order=SortOrder.DESCENDING)],
        )
        unique_uuids = {document.rbac.owner_id for document in search_results.documents}

        return len(unique_uuids)

    async def _get_users_with_notes(
        self, time_gte: datetime, time_lte: datetime, uuid_filter: Sequence[UUID] = None
    ) -> int:
        timestamp_range_query = CommonLeafQueries.timestamp_range_query(gte=time_gte, lte=time_lte)
        bool_not_query = NotQuery(
            queries=[PatternQuery(field_names=[f"{NoteFields.NOTE}"], pattern=DEFAULT_MOOD_EXPLANATION)]
        )
        and_query = AndQuery(queries=[timestamp_range_query, bool_not_query])
        if uuid_filter:
            values_query = ValuesQuery(
                field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}",
                values=[str(uuid) for uuid in uuid_filter],
            )
            and_query = AndQuery(queries=[and_query, values_query])

        single_query = SingleDocumentTypeQuery[Note](query=and_query, domain_type=Note)
        search_results = await self._search_service.search_documents_by_single_query(
            size=10000,
            query=single_query,
            sorts=[CommonSorts.timestamp(order=SortOrder.DESCENDING)],
        )
        unique_uuids = {document.rbac.owner_id for document in search_results.documents}
        return len(unique_uuids)

    async def _get_users_with_rating(
        self, time_gte: datetime, time_lte: datetime, uuid_filter: Sequence[UUID] = None
    ) -> int:
        timestamp_range_query = CommonLeafQueries.timestamp_range_query(gte=time_gte, lte=time_lte)
        bool_not_query = NotQuery(
            queries=[PatternQuery(field_names=[f"{StressFields.NOTE}"], pattern=DEFAULT_MOOD_EXPLANATION)]
        )
        and_query = AndQuery(queries=[timestamp_range_query, bool_not_query])
        if uuid_filter:
            values_query = ValuesQuery(
                field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}",
                values=[str(uuid) for uuid in uuid_filter],
            )
            and_query = AndQuery(queries=[and_query, values_query])

        query = SingleDocumentTypeQuery[Event](
            type_queries=[TypeQuery(query=and_query, domain_types=[Stress, Emotion])]
        )

        continuation_token = None
        continue_fetch = True
        documents: list[Event] = []
        while continue_fetch:
            fetched_documents = await self._search_service.search_documents_by_single_query(
                continuation_token=continuation_token,
                query=query,
                sorts=[CommonSorts.timestamp(order=SortOrder.DESCENDING)],
                size=1000,
            )
            continuation_token = fetched_documents.continuation_token
            continue_fetch = bool(fetched_documents.documents)
            documents.extend(fetched_documents.documents)

        unique_uuids = {document.rbac.owner_id for document in documents}

        return len(unique_uuids)
